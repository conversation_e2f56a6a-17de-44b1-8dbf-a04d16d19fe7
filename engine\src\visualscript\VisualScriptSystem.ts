/**
 * 视觉脚本系统
 * 负责管理和执行视觉脚本
 */
import { System } from '../core/System';
import { Entity } from '../core/Entity';
import { World } from '../core/World';
import { VisualScriptComponent } from './VisualScriptComponent';
import { VisualScriptEngine } from './VisualScriptEngine';
import { NodeRegistry } from './nodes/NodeRegistry';
import { ValueTypeRegistry } from './values/ValueTypeRegistry';
import { EventEmitter } from '../utils/EventEmitter';

// 导入预设节点注册函数
import { registerCoreNodes } from './presets/CoreNodes';
import { registerLogicNodes } from './presets/LogicNodes';
import { registerEntityNodes } from './presets/EntityNodes';
import { registerMathNodes } from './presets/MathNodes';
import { registerTimeNodes } from './presets/TimeNodes';
import { registerPhysicsNodes } from './presets/PhysicsNodes';
import { registerSoftBodyNodes } from './presets/SoftBodyNodes';
import { registerAnimationNodes } from './presets/AnimationNodes';
import { registerInputNodes } from './presets/InputNodes';
import { registerAudioNodes } from './presets/AudioNodes';
import { registerNetworkNodes } from './presets/NetworkNodes';
import { registerAINodes } from './presets/AINodes';

/**
 * 视觉脚本系统配置
 */
export interface VisualScriptSystemOptions {
  /** 是否自动初始化 */
  autoInit?: boolean;
  /** 默认脚本域 */
  defaultDomain?: string;
}

/**
 * 视觉脚本系统
 * 负责管理和执行视觉脚本
 */
export class VisualScriptSystem extends System {
  /** 系统优先级 */
  public static readonly PRIORITY = 200;

  /** 节点注册表 */
  private nodeRegistry: NodeRegistry;

  /** 值类型注册表 */
  private valueTypeRegistry: ValueTypeRegistry;

  /** 视觉脚本引擎实例映射 */
  private engineInstances: Map<Entity, VisualScriptEngine> = new Map();

  /** 默认脚本域 */
  private defaultDomain: string;

  /** 脚本域注册表 */
  private domainRegistries: Map<string, { nodes: NodeRegistry, values: ValueTypeRegistry }> = new Map();

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 世界引用 */
  private world: World | null = null;

  /**
   * 创建视觉脚本系统
   * @param options 系统选项
   */
  constructor(options: VisualScriptSystemOptions = {}) {
    super(VisualScriptSystem.PRIORITY);

    // 创建默认节点注册表和值类型注册表
    this.nodeRegistry = new NodeRegistry();
    this.valueTypeRegistry = new ValueTypeRegistry();

    // 设置默认脚本域
    this.defaultDomain = options.defaultDomain || 'default';

    // 注册默认脚本域
    this.registerDomain(this.defaultDomain, this.nodeRegistry, this.valueTypeRegistry);

    // 如果设置了自动初始化，则初始化系统
    if (options.autoInit !== false) {
      this.initialize();
    }
  }

  /**
   * 初始化系统
   * @param world 世界实例
   */
  public initialize(world?: World): void {
    super.initialize();

    // 设置世界引用
    if (world) {
      this.world = world;
    }

    if (!this.world) {
      console.error('VisualScriptSystem: 世界实例未设置');
      return;
    }

    // 注册核心节点和值类型
    this.registerCoreNodesAndValueTypes();

    // 监听实体添加和移除事件
    this.world.on('entityAdded', this.onEntityAdded.bind(this));
    this.world.on('entityRemoved', this.onEntityRemoved.bind(this));

    // 初始化现有实体
    this.world.getEntities().forEach(entity => {
      if (entity.hasComponent(VisualScriptComponent.TYPE)) {
        this.initializeEntityScript(entity);
      }
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 更新所有视觉脚本引擎实例
    for (const [entity, engine] of this.engineInstances.entries()) {
      const component = entity.getComponent(VisualScriptComponent.TYPE) as VisualScriptComponent;

      // 如果组件正在运行，则执行引擎
      if (component && component.running) {
        engine.update(deltaTime);
      }
    }
  }

  /**
   * 注册脚本域
   * @param domain 脚本域名称
   * @param nodeRegistry 节点注册表
   * @param valueTypeRegistry 值类型注册表
   */
  public registerDomain(domain: string, nodeRegistry: NodeRegistry, valueTypeRegistry: ValueTypeRegistry): void {
    this.domainRegistries.set(domain, { nodes: nodeRegistry, values: valueTypeRegistry });
    this.eventEmitter.emit('domainRegistered', domain, nodeRegistry, valueTypeRegistry);
  }

  /**
   * 获取脚本域注册表
   * @param domain 脚本域名称
   * @returns 脚本域注册表
   */
  public getDomainRegistry(domain: string): { nodes: NodeRegistry, values: ValueTypeRegistry } | undefined {
    return this.domainRegistries.get(domain);
  }

  /**
   * 获取默认脚本域
   * @returns 默认脚本域名称
   */
  public getDefaultDomain(): string {
    return this.defaultDomain;
  }

  /**
   * 设置默认脚本域
   * @param domain 默认脚本域名称
   */
  public setDefaultDomain(domain: string): void {
    this.defaultDomain = domain;
  }

  /**
   * 注册核心节点和值类型
   */
  private registerCoreNodesAndValueTypes(): void {
    // 获取默认域的注册表
    const defaultRegistry = this.getDomainRegistry(this.defaultDomain);

    if (defaultRegistry) {
      // 注册核心节点
      registerCoreNodes(defaultRegistry.nodes);

      // 注册逻辑节点
      registerLogicNodes(defaultRegistry.nodes);

      // 注册实体节点
      registerEntityNodes(defaultRegistry.nodes);

      // 注册数学节点
      registerMathNodes(defaultRegistry.nodes);

      // 注册时间节点
      registerTimeNodes(defaultRegistry.nodes);

      // 注册物理节点
      registerPhysicsNodes(defaultRegistry.nodes);

      // 注册软体物理节点
      registerSoftBodyNodes(defaultRegistry.nodes);

      // 注册动画节点
      registerAnimationNodes(defaultRegistry.nodes);

      // 注册输入节点
      registerInputNodes(defaultRegistry.nodes);

      // 注册音频节点
      registerAudioNodes(defaultRegistry.nodes);

      // 注册网络节点
      registerNetworkNodes(defaultRegistry.nodes);

      // 注册AI节点
      registerAINodes(defaultRegistry.nodes);

      console.log('已注册所有预设节点类型');
    }
  }

  /**
   * 实体添加事件处理
   * @param entity 添加的实体
   */
  private onEntityAdded(entity: Entity): void {
    // 如果实体有视觉脚本组件，则初始化脚本
    if (entity.hasComponent(VisualScriptComponent.TYPE)) {
      this.initializeEntityScript(entity);
    }

    // 监听实体组件添加事件
    entity.on('componentAdded', (component) => {
      if (component.type === VisualScriptComponent.TYPE) {
        this.initializeEntityScript(entity);
      }
    });

    // 监听实体组件移除事件
    entity.on('componentRemoved', (component) => {
      if (component.type === VisualScriptComponent.TYPE) {
        this.cleanupEntityScript(entity);
      }
    });
  }

  /**
   * 实体移除事件处理
   * @param entity 移除的实体
   */
  private onEntityRemoved(entity: Entity): void {
    // 清理实体脚本
    this.cleanupEntityScript(entity);
  }

  /**
   * 初始化实体脚本
   * @param entity 实体
   */
  private initializeEntityScript(entity: Entity): void {
    // 获取视觉脚本组件
    const component = entity.getComponent(VisualScriptComponent.TYPE) as VisualScriptComponent;

    // 如果已经有引擎实例，则先清理
    if (this.engineInstances.has(entity)) {
      this.cleanupEntityScript(entity);
    }

    // 如果没有脚本数据，则不创建引擎实例
    if (!component || !component.script) {
      return;
    }

    // 获取脚本域注册表
    const domainRegistry = this.getDomainRegistry(component.domain) ||
                          this.getDomainRegistry(this.defaultDomain);

    if (!domainRegistry) {
      console.error(`找不到脚本域: ${component.domain}`);
      return;
    }

    // 创建视觉脚本引擎实例
    const engine = new VisualScriptEngine({
      script: component.script,
      nodeRegistry: domainRegistry.nodes,
      valueTypeRegistry: domainRegistry.values,
      entity: entity,
      world: this.world!
    });

    // 存储引擎实例
    this.engineInstances.set(entity, engine);

    // 监听组件事件
    component.on('scriptChanged', () => {
      this.initializeEntityScript(entity);
    });

    component.on('runningChanged', (running) => {
      if (running) {
        engine.start();
      } else {
        engine.stop();
      }
    });

    component.on('domainChanged', () => {
      this.initializeEntityScript(entity);
    });

    // 如果组件设置为自动运行，则启动引擎
    if (component.running) {
      engine.start();
    }
  }

  /**
   * 清理实体脚本
   * @param entity 实体
   */
  private cleanupEntityScript(entity: Entity): void {
    // 获取引擎实例
    const engine = this.engineInstances.get(entity);

    if (engine) {
      // 停止引擎
      engine.stop();

      // 销毁引擎
      engine.dispose();

      // 移除引擎实例
      this.engineInstances.delete(entity);
    }
  }

  /**
   * 销毁系统
   */
  public dispose(): void {
    // 清理所有引擎实例
    for (const [entity, engine] of this.engineInstances.entries()) {
      engine.stop();
      engine.dispose();
    }

    // 清空引擎实例映射
    this.engineInstances.clear();

    // 移除事件监听
    if (this.world) {
      this.world.off('entityAdded', this.onEntityAdded);
      this.world.off('entityRemoved', this.onEntityRemoved);
    }

    super.dispose();
  }
}
