/**
 * 物理体组件
 * 为实体提供物理属性和行为
 */
import * as CANNON from 'cannon-es';
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';

/**
 * 物理体类型
 */
export enum BodyType {
  /** 静态物体 */
  STATIC = 'static',
  /** 动态物体 */
  DYNAMIC = 'dynamic',
  /** 运动学物体 */
  KINEMATIC = 'kinematic'
}

/**
 * 物理体组件选项
 */
export interface PhysicsBodyOptions {
  /** 物理体类型 */
  type?: BodyType;
  /** 质量 */
  mass?: number;
  /** 位置 */
  position?: THREE.Vector3;
  /** 旋转 */
  quaternion?: THREE.Quaternion;
  /** 线性阻尼 */
  linearDamping?: number;
  /** 角阻尼 */
  angularDamping?: number;
  /** 是否允许休眠 */
  allowSleep?: boolean;
  /** 休眠速度阈值 */
  sleepSpeedLimit?: number;
  /** 休眠时间阈值 */
  sleepTimeLimit?: number;
  /** 碰撞过滤组 */
  collisionFilterGroup?: number;
  /** 碰撞过滤掩码 */
  collisionFilterMask?: number;
  /** 物理材质 */
  material?: CANNON.Material;
  /** 是否固定旋转 */
  fixedRotation?: boolean;
  /** 是否自动更新变换 */
  autoUpdateTransform?: boolean;
}

/**
 * 物理体组件
 */
export class PhysicsBodyComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'PhysicsBodyComponent';

  /** 物理体类型 */
  public bodyType: BodyType;

  /** 质量 */
  public mass: number;

  /** 位置 */
  public position: THREE.Vector3;

  /** 旋转 */
  public quaternion: THREE.Quaternion;

  /** 线性阻尼 */
  public linearDamping: number;

  /** 角阻尼 */
  public angularDamping: number;

  /** 是否允许休眠 */
  public allowSleep: boolean;

  /** 休眠速度阈值 */
  public sleepSpeedLimit: number;

  /** 休眠时间阈值 */
  public sleepTimeLimit: number;

  /** 碰撞过滤组 */
  public collisionFilterGroup: number;

  /** 碰撞过滤掩码 */
  public collisionFilterMask: number;

  /** 物理材质 */
  public material: CANNON.Material | null;

  /** 是否固定旋转 */
  public fixedRotation: boolean;

  /** 是否自动更新变换 */
  public autoUpdateTransform: boolean;

  /** CANNON.js物理体 */
  private body: CANNON.Body | null = null;

  /** 物理世界 */
  private world: CANNON.World | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否已销毁 */
  private destroyed: boolean = false;

  /**
   * 创建物理体组件
   * @param options 物理体选项
   */
  constructor(options: PhysicsBodyOptions = {}) {
    super(PhysicsBodyComponent.type);

    this.bodyType = options.type || BodyType.DYNAMIC;
    this.mass = this.bodyType === BodyType.STATIC ? 0 : (options.mass || 1);
    this.position = options.position ? options.position.clone() : new THREE.Vector3();
    this.quaternion = options.quaternion ? options.quaternion.clone() : new THREE.Quaternion();
    this.linearDamping = options.linearDamping !== undefined ? options.linearDamping : 0.01;
    this.angularDamping = options.angularDamping !== undefined ? options.angularDamping : 0.01;
    this.allowSleep = options.allowSleep !== undefined ? options.allowSleep : true;
    this.sleepSpeedLimit = options.sleepSpeedLimit || 0.1;
    this.sleepTimeLimit = options.sleepTimeLimit || 1;
    this.collisionFilterGroup = options.collisionFilterGroup || 1;
    this.collisionFilterMask = options.collisionFilterMask || -1;
    this.material = options.material || null;
    this.fixedRotation = options.fixedRotation || false;
    this.autoUpdateTransform = options.autoUpdateTransform !== undefined ? options.autoUpdateTransform : true;
  }

  /**
   * 初始化物理体
   * @param world 物理世界
   */
  public initialize(world: CANNON.World): void {
    if (this.initialized || !this.getEntity() || this.destroyed) return;

    this.world = world;

    // 从实体的变换组件获取初始位置和旋转
    const entity = this.getEntity();
    const transform = entity?.getTransform();
    if (transform) {
      this.position.copy(transform.getPosition());
      this.quaternion.copy(transform.getRotationQuaternion());
    }

    // 创建物理体
    this.body = new CANNON.Body({
      mass: this.mass,
      position: new CANNON.Vec3(this.position.x, this.position.y, this.position.z),
      quaternion: new CANNON.Quaternion(this.quaternion.x, this.quaternion.y, this.quaternion.z, this.quaternion.w),
      linearDamping: this.linearDamping,
      angularDamping: this.angularDamping,
      allowSleep: this.allowSleep,
      sleepSpeedLimit: this.sleepSpeedLimit,
      sleepTimeLimit: this.sleepTimeLimit,
      collisionFilterGroup: this.collisionFilterGroup,
      collisionFilterMask: this.collisionFilterMask,
      material: this.material || undefined,
      type: this.getBodyType(),
      fixedRotation: this.fixedRotation
    });

    // 设置用户数据（使用自定义属性）
    (this.body as any).userData = {
      entity: entity
    };

    // 添加到物理世界
    world.addBody(this.body);

    this.initialized = true;
  }

  /**
   * 获取CANNON.js物理体类型
   * @returns CANNON.js物理体类型
   */
  private getBodyType(): CANNON.BodyType {
    switch (this.bodyType) {
      case BodyType.STATIC:
        return CANNON.BODY_TYPES.STATIC;
      case BodyType.DYNAMIC:
        return CANNON.BODY_TYPES.DYNAMIC;
      case BodyType.KINEMATIC:
        return CANNON.BODY_TYPES.KINEMATIC;
      default:
        return CANNON.BODY_TYPES.DYNAMIC;
    }
  }

  /**
   * 更新实体变换
   */
  public updateTransform(): void {
    if (!this.initialized || !this.body || !this.getEntity() || !this.autoUpdateTransform) return;

    const entity = this.getEntity();
    const transform = entity?.getTransform();
    if (!transform) return;

    // 如果是静态或运动学物体，则从变换组件更新物理体
    if (this.bodyType === BodyType.STATIC || this.bodyType === BodyType.KINEMATIC) {
      const position = transform.getPosition();
      const quaternion = transform.getRotationQuaternion();

      this.body.position.set(position.x, position.y, position.z);
      this.body.quaternion.set(quaternion.x, quaternion.y, quaternion.z, quaternion.w);

      // 如果是运动学物体，则需要更新速度
      if (this.bodyType === BodyType.KINEMATIC) {
        this.body.velocity.setZero();
        this.body.angularVelocity.setZero();
      }
    }
    // 如果是动态物体，则从物理体更新变换组件
    else if (this.bodyType === BodyType.DYNAMIC) {
      transform.setPosition(
        this.body.position.x,
        this.body.position.y,
        this.body.position.z
      );

      transform.setRotationQuaternion(
        this.body.quaternion.x,
        this.body.quaternion.y,
        this.body.quaternion.z,
        this.body.quaternion.w
      );
    }
  }

  /**
   * 应用力
   * @param force 力向量
   * @param worldPoint 世界坐标中的作用点（可选）
   */
  public applyForce(force: THREE.Vector3, worldPoint?: THREE.Vector3): void {
    if (!this.initialized || !this.body) return;

    const forceVec = new CANNON.Vec3(force.x, force.y, force.z);

    if (worldPoint) {
      const pointVec = new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z);
      this.body.applyForce(forceVec, pointVec);
    } else {
      this.body.applyForce(forceVec, this.body.position);
    }
  }

  /**
   * 应用冲量
   * @param impulse 冲量向量
   * @param worldPoint 世界坐标中的作用点（可选）
   */
  public applyImpulse(impulse: THREE.Vector3, worldPoint?: THREE.Vector3): void {
    if (!this.initialized || !this.body) return;

    const impulseVec = new CANNON.Vec3(impulse.x, impulse.y, impulse.z);

    if (worldPoint) {
      const pointVec = new CANNON.Vec3(worldPoint.x, worldPoint.y, worldPoint.z);
      this.body.applyImpulse(impulseVec, pointVec);
    } else {
      this.body.applyImpulse(impulseVec, this.body.position);
    }
  }

  /**
   * 应用扭矩
   * @param torque 扭矩向量
   */
  public applyTorque(torque: THREE.Vector3): void {
    if (!this.initialized || !this.body) return;

    const torqueVec = new CANNON.Vec3(torque.x, torque.y, torque.z);
    this.body.applyTorque(torqueVec);
  }

  /**
   * 设置线性速度
   * @param velocity 线性速度向量
   */
  public setLinearVelocity(velocity: THREE.Vector3): void {
    if (!this.initialized || !this.body) return;

    this.body.velocity.set(velocity.x, velocity.y, velocity.z);
  }

  /**
   * 获取线性速度
   * @returns 线性速度向量
   */
  public getLinearVelocity(): THREE.Vector3 {
    if (!this.initialized || !this.body) return new THREE.Vector3();

    return new THREE.Vector3(
      this.body.velocity.x,
      this.body.velocity.y,
      this.body.velocity.z
    );
  }

  /**
   * 设置角速度
   * @param angularVelocity 角速度向量
   */
  public setAngularVelocity(angularVelocity: THREE.Vector3): void {
    if (!this.initialized || !this.body) return;

    this.body.angularVelocity.set(
      angularVelocity.x,
      angularVelocity.y,
      angularVelocity.z
    );
  }

  /**
   * 获取角速度
   * @returns 角速度向量
   */
  public getAngularVelocity(): THREE.Vector3 {
    if (!this.initialized || !this.body) return new THREE.Vector3();

    return new THREE.Vector3(
      this.body.angularVelocity.x,
      this.body.angularVelocity.y,
      this.body.angularVelocity.z
    );
  }

  /**
   * 设置物理体类型
   * @param type 物理体类型
   */
  public setBodyType(type: BodyType): void {
    if (this.bodyType === type) return;

    this.bodyType = type;

    if (this.body) {
      // 更新质量
      if (type === BodyType.STATIC) {
        this.mass = 0;
      } else if (this.mass === 0) {
        this.mass = 1;
      }

      // 更新物理体类型
      const bodyType = this.getBodyType();
      (this.body as any).type = bodyType;
      this.body.mass = this.mass;
      this.body.updateMassProperties();
    }
  }

  /**
   * 获取CANNON.js物理体
   * @returns CANNON.js物理体
   */
  public getCannonBody(): CANNON.Body | null {
    return this.body;
  }

  /**
   * 碰撞开始回调
   * @param otherEntity 碰撞的另一个实体
   * @param contact 碰撞接触点
   */
  public onCollisionStart(_otherEntity: Entity, _contact: CANNON.ContactEquation): void {
    // 可以在子类中重写此方法
  }

  /**
   * 碰撞结束回调
   * @param otherEntity 碰撞的另一个实体
   * @param contact 碰撞接触点
   */
  public onCollisionEnd(_otherEntity: Entity, _contact: CANNON.ContactEquation): void {
    // 可以在子类中重写此方法
  }

  /**
   * 销毁物理体
   */
  public dispose(): void {
    if (this.destroyed) return;

    if (this.initialized && this.body && this.world) {
      this.world.removeBody(this.body);
      this.body = null;
      this.world = null;
      this.initialized = false;
    }

    this.destroyed = true;

    super.dispose();
  }
}
