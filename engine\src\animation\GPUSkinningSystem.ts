/**
 * GPU蒙皮系统
 * 使用GPU加速蒙皮动画计算
 */
import * as THREE from 'three';
import { System } from '../core/System';
import { Entity } from '../core/Entity';
import { Camera } from '../rendering/Camera';
import { Scene } from '../scene/Scene';
import { Transform } from '../scene/Transform';
import { AnimationComponent } from './AnimationComponent';
import { SkinnedMeshComponent } from './SkinnedMeshComponent';
import { Debug } from '../utils/Debug';

/**
 * GPU蒙皮系统配置接口
 */
export interface GPUSkinningSystemOptions {
  /** 是否使用GPU蒙皮 */
  useGPUSkinning?: boolean;
  /** 是否使用动画实例化 */
  useAnimationInstancing?: boolean;
  /** 是否使用动画合并 */
  useAnimationMerging?: boolean;
  /** 是否使用动画LOD */
  useAnimationLOD?: boolean;
  /** 是否使用动画缓存 */
  useAnimationCache?: boolean;
  /** 是否使用动画压缩 */
  useAnimationCompression?: boolean;
  /** 是否使用调试可视化 */
  useDebugVisualization?: boolean;
  /** 最大骨骼数量 */
  maxBones?: number;
  /** 最大实例数量 */
  maxInstances?: number;
  /** 更新间隔（帧） */
  updateInterval?: number;
}

/**
 * 动画实例数据接口
 */
export interface AnimationInstanceData {
  /** 实例ID */
  id: string;
  /** 实体 */
  entity: Entity;
  /** 动画组件 */
  animationComponent: AnimationComponent;
  /** 蒙皮网格组件 */
  skinnedMeshComponent: SkinnedMeshComponent;
  /** 骨骼矩阵 */
  boneMatrices: Float32Array;
  /** 动画时间 */
  animationTime: number;
  /** 动画权重 */
  animationWeight: number;
  /** 动画速度 */
  animationSpeed: number;
  /** 是否循环 */
  loop: boolean;
  /** 是否暂停 */
  paused: boolean;
  /** 是否可见 */
  visible: boolean;
  /** 是否需要更新 */
  needsUpdate: boolean;
  /** 用户数据 */
  userData: any;
}

/**
 * 动画批处理组接口
 */
export interface AnimationBatchGroup {
  /** 批处理组ID */
  id: string;
  /** 骨骼数量 */
  boneCount: number;
  /** 实例数量 */
  instanceCount: number;
  /** 骨骼纹理 */
  boneTexture: THREE.DataTexture | null;
  /** 骨骼纹理大小 */
  boneTextureSize: number;
  /** 骨骼矩阵数组 */
  boneMatrices: Float32Array;
  /** 实例数据列表 */
  instances: AnimationInstanceData[];
  /** 实例到索引的映射 */
  instanceToIndex: Map<string, number>;
  /** 可用索引列表 */
  availableIndices: number[];
  /** 是否需要更新 */
  needsUpdate: boolean;
  /** 是否可见 */
  visible: boolean;
  /** 用户数据 */
  userData: any;
}

/**
 * GPU蒙皮系统类
 */
export class GPUSkinningSystem extends System {
  /** 系统类型 */
  private static readonly TYPE: string = 'GPUSkinningSystem';

  /** 是否使用GPU蒙皮 */
  private useGPUSkinning: boolean;

  /** 是否使用动画实例化 */
  private useAnimationInstancing: boolean;

  /** 是否使用动画合并 */
  private useAnimationMerging: boolean;

  /** 是否使用动画LOD */
  private useAnimationLOD: boolean;

  /** 是否使用动画缓存 */
  private useAnimationCache: boolean;

  /** 是否使用动画压缩 */
  private useAnimationCompression: boolean;

  /** 是否使用调试可视化 */
  private useDebugVisualization: boolean;

  /** 最大骨骼数量 */
  private maxBones: number;

  /** 最大实例数量 */
  private maxInstances: number;

  /** 更新间隔（帧） */
  private updateInterval: number;

  /** 当前帧计数 */
  private frameCount: number = 0;

  /** 动画批处理组列表 */
  private batchGroups: Map<string, AnimationBatchGroup> = new Map();

  /** 实体到批处理组的映射 */
  private entityToBatchGroup: Map<Entity, AnimationBatchGroup> = new Map();

  /** 实体到实例数据的映射 */
  private entityToInstanceData: Map<Entity, AnimationInstanceData> = new Map();

  /** 实例计数器 */
  private instanceCounter: number = 0;

  /** 批处理组计数器 */
  private batchGroupCounter: number = 0;

  /** 骨骼矩阵计算着色器 */
  private boneMatrixShader: THREE.ShaderMaterial | null = null;

  /** 骨骼矩阵计算渲染目标 */
  private boneMatrixRenderTarget: THREE.WebGLRenderTarget | null = null;

  /** 骨骼矩阵计算场景 */
  private boneMatrixScene: THREE.Scene | null = null;

  /** 骨骼矩阵计算相机 */
  private boneMatrixCamera: THREE.OrthographicCamera | null = null;

  /** 骨骼矩阵计算网格 */
  private boneMatrixMesh: THREE.Mesh | null = null;

  /** 调试可视化材质 */
  private debugMaterial: THREE.MeshBasicMaterial | null = null;

  /** 调试可视化网格 */
  private debugMeshes: THREE.Mesh[] = [];

  /** 是否支持GPU蒙皮 */
  private supportsGPUSkinning: boolean = false;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建GPU蒙皮系统
   * @param options GPU蒙皮系统配置
   */
  constructor(options: GPUSkinningSystemOptions = {}) {
    super();

    this.useGPUSkinning = options.useGPUSkinning !== undefined ? options.useGPUSkinning : true;
    this.useAnimationInstancing = options.useAnimationInstancing !== undefined ? options.useAnimationInstancing : true;
    this.useAnimationMerging = options.useAnimationMerging !== undefined ? options.useAnimationMerging : false;
    this.useAnimationLOD = options.useAnimationLOD !== undefined ? options.useAnimationLOD : false;
    this.useAnimationCache = options.useAnimationCache !== undefined ? options.useAnimationCache : true;
    this.useAnimationCompression = options.useAnimationCompression !== undefined ? options.useAnimationCompression : false;
    this.useDebugVisualization = options.useDebugVisualization !== undefined ? options.useDebugVisualization : false;
    this.maxBones = options.maxBones || 200;
    this.maxInstances = options.maxInstances || 100;
    this.updateInterval = options.updateInterval || 1;

    // 检查GPU蒙皮支持
    this.checkGPUSkinningSupport();
  }

  /**
   * 检查GPU蒙皮支持
   */
  private checkGPUSkinningSupport(): void {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2');
      this.supportsGPUSkinning = !!gl;

      if (!this.supportsGPUSkinning && this.useGPUSkinning) {
        Debug.warn('GPUSkinningSystem', 'GPU蒙皮不支持，回退到CPU蒙皮');
        this.useGPUSkinning = false;
      }
    } catch (error) {
      this.supportsGPUSkinning = false;
      Debug.warn('GPUSkinningSystem', 'GPU蒙皮不支持，回退到CPU蒙皮');
      this.useGPUSkinning = false;
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 初始化骨骼矩阵计算
    if (this.useGPUSkinning && this.supportsGPUSkinning) {
      this.initializeGPUSkinning();
    }

    // 初始化调试可视化
    if (this.useDebugVisualization) {
      this.initializeDebugVisualization();
    }

    this.initialized = true;
  }

  /**
   * 初始化GPU蒙皮
   */
  private initializeGPUSkinning(): void {
    // 创建骨骼矩阵计算场景和相机
    this.boneMatrixScene = new THREE.Scene();
    this.boneMatrixCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);

    // 创建骨骼矩阵计算着色器
    this.boneMatrixShader = new THREE.ShaderMaterial({
      vertexShader: `
        varying vec2 vUv;

        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        varying vec2 vUv;
        uniform sampler2D boneTexture;
        uniform sampler2D animationTexture;
        uniform float boneCount;
        uniform float time;
        uniform float weight;

        void main() {
          // 计算骨骼索引
          float boneIndex = floor(vUv.x * boneCount);

          // 获取骨骼矩阵
          vec4 v1 = texture2D(boneTexture, vec2((boneIndex * 4.0 + 0.5) / (boneCount * 4.0), vUv.y));
          vec4 v2 = texture2D(boneTexture, vec2((boneIndex * 4.0 + 1.5) / (boneCount * 4.0), vUv.y));
          vec4 v3 = texture2D(boneTexture, vec2((boneIndex * 4.0 + 2.5) / (boneCount * 4.0), vUv.y));
          vec4 v4 = texture2D(boneTexture, vec2((boneIndex * 4.0 + 3.5) / (boneCount * 4.0), vUv.y));

          // 输出骨骼矩阵
          gl_FragColor = v1;
        }
      `,
      uniforms: {
        boneTexture: { value: null },
        animationTexture: { value: null },
        boneCount: { value: 0 },
        time: { value: 0 },
        weight: { value: 0 }
      }
    });

    // 创建骨骼矩阵计算网格
    const geometry = new THREE.PlaneGeometry(2, 2);
    this.boneMatrixMesh = new THREE.Mesh(geometry, this.boneMatrixShader);
    this.boneMatrixScene.add(this.boneMatrixMesh);

    // 创建骨骼矩阵计算渲染目标
    this.boneMatrixRenderTarget = new THREE.WebGLRenderTarget(
      this.maxBones * 4,
      this.maxInstances,
      {
        format: THREE.RGBAFormat,
        type: THREE.FloatType,
        minFilter: THREE.NearestFilter,
        magFilter: THREE.NearestFilter,
        stencilBuffer: false,
        depthBuffer: false
      }
    );
  }

  /**
   * 初始化调试可视化
   */
  private initializeDebugVisualization(): void {
    // 创建调试材质
    this.debugMaterial = new THREE.MeshBasicMaterial({
      wireframe: true,
      transparent: true,
      opacity: 0.5
    });
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 增加帧计数
    this.frameCount++;

    // 检查是否需要更新
    if (this.frameCount % this.updateInterval !== 0) {
      return;
    }

    // 获取相机和场景
    const camera = this.getCamera();
    const scene = this.getScene();

    if (!camera || !scene) {
      return;
    }

    // 更新动画组件
    this.updateAnimationComponents(deltaTime);

    // 更新蒙皮网格组件
    this.updateSkinnedMeshComponents(deltaTime);

    // 更新批处理组
    this.updateBatchGroups(camera);

    // 更新调试可视化
    if (this.useDebugVisualization) {
      this.updateDebugVisualization();
    }
  }

  /**
   * 获取相机
   * @returns 相机
   */
  private getCamera(): Camera | null {
    // TODO: 需要从世界或场景中获取相机
    return null;
  }

  /**
   * 获取场景
   * @returns 场景
   */
  private getScene(): Scene | null {
    // TODO: 需要从世界中获取场景
    return null;
  }

  /**
   * 更新动画组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateAnimationComponents(deltaTime: number): void {
    // TODO: 需要从世界中获取动画组件
    // 暂时跳过
  }

  /**
   * 更新蒙皮网格组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateSkinnedMeshComponents(deltaTime: number): void {
    // TODO: 需要从世界中获取蒙皮网格组件
    // 暂时跳过

  }

  /**
   * 使用CPU蒙皮更新
   * @param entity 实体
   * @param animationComponent 动画组件
   * @param skinnedMeshComponent 蒙皮网格组件
   */
  private updateWithCPUSkinning(entity: Entity, animationComponent: AnimationComponent, skinnedMeshComponent: SkinnedMeshComponent): void {
    // 获取蒙皮网格
    const skinnedMesh = skinnedMeshComponent.getSkinnedMesh();
    if (!skinnedMesh) {
      return;
    }

    // 更新骨骼矩阵
    skinnedMesh.skeleton.update();
  }

  /**
   * 添加到批处理组
   * @param entity 实体
   * @param animationComponent 动画组件
   * @param skinnedMeshComponent 蒙皮网格组件
   */
  private addToBatchGroup(entity: Entity, animationComponent: AnimationComponent, skinnedMeshComponent: SkinnedMeshComponent): void {
    // 获取蒙皮网格
    const skinnedMesh = skinnedMeshComponent.getSkinnedMesh();
    if (!skinnedMesh) {
      return;
    }

    // 获取骨骼数量
    const boneCount = skinnedMesh.skeleton.bones.length;
    if (boneCount === 0) {
      return;
    }

    // 查找合适的批处理组
    let batchGroup = this.findBatchGroup(boneCount);
    if (!batchGroup) {
      // 创建新的批处理组
      batchGroup = this.createBatchGroup(boneCount);
    }

    // 创建实例数据
    const instanceId = `instance_${this.instanceCounter++}`;
    const instanceData: AnimationInstanceData = {
      id: instanceId,
      entity,
      animationComponent,
      skinnedMeshComponent,
      boneMatrices: new Float32Array(boneCount * 16),
      animationTime: 0,
      animationWeight: 1,
      animationSpeed: 1,
      loop: true,
      paused: false,
      visible: true,
      needsUpdate: true,
      userData: {}
    };

    // 获取可用索引
    const index = batchGroup.availableIndices.pop()!;

    // 添加实例到批处理组
    batchGroup.instances[index] = instanceData;
    batchGroup.instanceToIndex.set(instanceId, index);
    batchGroup.needsUpdate = true;

    // 添加到映射
    this.entityToBatchGroup.set(entity, batchGroup);
    this.entityToInstanceData.set(entity, instanceData);

    // 设置蒙皮网格使用骨骼纹理
    const material = Array.isArray(skinnedMesh.material) ? skinnedMesh.material[0] : skinnedMesh.material;
    if (material && 'uniforms' in material) {
      const shaderMaterial = material as THREE.ShaderMaterial;
      shaderMaterial.uniforms = shaderMaterial.uniforms || {};
      shaderMaterial.uniforms.boneTexture = { value: batchGroup.boneTexture };
      shaderMaterial.uniforms.boneTextureSize = { value: batchGroup.boneTextureSize };
      shaderMaterial.needsUpdate = true;
    }
  }

  /**
   * 查找批处理组
   * @param boneCount 骨骼数量
   * @returns 批处理组
   */
  private findBatchGroup(boneCount: number): AnimationBatchGroup | null {
    // 遍历所有批处理组
    for (const batchGroup of this.batchGroups.values()) {
      // 检查骨骼数量是否匹配
      if (batchGroup.boneCount === boneCount) {
        // 检查是否有可用索引
        if (batchGroup.availableIndices.length > 0) {
          return batchGroup;
        }
      }
    }

    return null;
  }

  /**
   * 创建批处理组
   * @param boneCount 骨骼数量
   * @returns 批处理组
   */
  private createBatchGroup(boneCount: number): AnimationBatchGroup {
    // 生成批处理组ID
    const batchGroupId = `batch_${this.batchGroupCounter++}`;

    // 计算骨骼纹理大小
    const boneTextureSize = Math.max(4, Math.pow(2, Math.ceil(Math.log2(Math.sqrt(boneCount * 4)))));

    // 创建骨骼矩阵数组
    const boneMatrices = new Float32Array(this.maxInstances * boneCount * 16);

    // 创建骨骼纹理
    const boneTexture = new THREE.DataTexture(
      boneMatrices,
      boneCount * 4,
      this.maxInstances,
      THREE.RGBAFormat,
      THREE.FloatType
    );
    boneTexture.needsUpdate = true;

    // 创建批处理组
    const batchGroup: AnimationBatchGroup = {
      id: batchGroupId,
      boneCount,
      instanceCount: this.maxInstances,
      boneTexture,
      boneTextureSize,
      boneMatrices,
      instances: new Array(this.maxInstances),
      instanceToIndex: new Map(),
      availableIndices: Array.from({ length: this.maxInstances }, (_, i) => i),
      needsUpdate: true,
      visible: true,
      userData: {}
    };

    // 添加到批处理组列表
    this.batchGroups.set(batchGroupId, batchGroup);

    return batchGroup;
  }

  /**
   * 更新批处理组
   * @param camera 相机
   */
  private updateBatchGroups(camera: Camera): void {
    // 遍历所有批处理组
    for (const batchGroup of this.batchGroups.values()) {
      // 如果批处理组不需要更新，则跳过
      if (!batchGroup.needsUpdate) {
        continue;
      }

      // 更新批处理组
      this.updateBatchGroup(batchGroup, camera);

      // 重置更新标志
      batchGroup.needsUpdate = false;
    }
  }

  /**
   * 更新批处理组
   * @param batchGroup 批处理组
   * @param camera 相机
   */
  private updateBatchGroup(batchGroup: AnimationBatchGroup, camera: Camera): void {
    // 如果使用GPU蒙皮，则使用GPU更新骨骼矩阵
    if (this.useGPUSkinning && this.supportsGPUSkinning) {
      this.updateWithGPUSkinning(batchGroup);
    } else {
      // 否则使用CPU更新骨骼矩阵
      this.updateWithCPUBatching(batchGroup);
    }
  }

  /**
   * 使用GPU蒙皮更新
   * @param batchGroup 批处理组
   */
  private updateWithGPUSkinning(batchGroup: AnimationBatchGroup): void {
    // 获取渲染器 (需要从外部传入或通过其他方式获取)
    const renderer = null; // TODO: 需要正确的渲染器实例
    if (!renderer || !this.boneMatrixScene || !this.boneMatrixCamera || !this.boneMatrixRenderTarget || !this.boneMatrixShader || !this.boneMatrixMesh) {
      return;
    }

    // 设置骨骼矩阵着色器的参数
    this.boneMatrixShader.uniforms.boneTexture.value = batchGroup.boneTexture;
    this.boneMatrixShader.uniforms.boneCount.value = batchGroup.boneCount;

    // 渲染骨骼矩阵
    renderer.setRenderTarget(this.boneMatrixRenderTarget);
    renderer.render(this.boneMatrixScene, this.boneMatrixCamera);
    renderer.setRenderTarget(null);

    // 更新骨骼纹理
    batchGroup.boneTexture!.needsUpdate = true;
  }

  /**
   * 使用CPU批处理更新
   * @param batchGroup 批处理组
   */
  private updateWithCPUBatching(batchGroup: AnimationBatchGroup): void {
    // 遍历所有实例
    for (let i = 0; i < batchGroup.instances.length; i++) {
      // 获取实例数据
      const instance = batchGroup.instances[i];
      if (!instance) {
        continue;
      }

      // 如果实例不可见或暂停，则跳过
      if (!instance.visible || instance.paused) {
        continue;
      }

      // 获取蒙皮网格
      const skinnedMesh = instance.skinnedMeshComponent.getSkinnedMesh();
      if (!skinnedMesh) {
        continue;
      }

      // 更新骨骼矩阵
      skinnedMesh.skeleton.update();

      // 复制骨骼矩阵到实例数据
      const boneMatrices = skinnedMesh.skeleton.boneMatrices;
      instance.boneMatrices.set(boneMatrices);

      // 复制骨骼矩阵到批处理组
      const offset = i * batchGroup.boneCount * 16;
      batchGroup.boneMatrices.set(instance.boneMatrices, offset);
    }

    // 更新骨骼纹理
    batchGroup.boneTexture!.needsUpdate = true;
  }

  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
    }
    this.debugMeshes = [];

    // 获取场景
    const scene = this.getScene();
    if (!scene) {
      return;
    }

    // 可视化批处理组
    for (const batchGroup of this.batchGroups.values()) {
      // 创建批处理组调试网格
      const batchMesh = new THREE.Mesh(
        new THREE.BoxGeometry(1, 1, 1),
        this.debugMaterial!.clone()
      );

      // 设置批处理组调试网格颜色
      (batchMesh.material as THREE.MeshBasicMaterial).color.set(0x00ff00); // 绿色

      // 设置批处理组调试网格位置
      batchMesh.position.set(0, 0, 0);
      batchMesh.scale.set(0.5, 0.5, 0.5);

      // 添加到场景
      scene.getObject3D().add(batchMesh);
      this.debugMeshes.push(batchMesh);
    }
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 清除批处理组
    for (const batchGroup of this.batchGroups.values()) {
      // 销毁骨骼纹理
      if (batchGroup.boneTexture) {
        batchGroup.boneTexture.dispose();
      }
    }

    // 清除映射
    this.batchGroups.clear();
    this.entityToBatchGroup.clear();
    this.entityToInstanceData.clear();

    // 销毁骨骼矩阵计算资源
    if (this.boneMatrixRenderTarget) {
      this.boneMatrixRenderTarget.dispose();
      this.boneMatrixRenderTarget = null;
    }

    if (this.boneMatrixMesh) {
      this.boneMatrixMesh.geometry.dispose();
      (this.boneMatrixMesh.material as THREE.Material).dispose();
      this.boneMatrixMesh = null;
    }

    if (this.boneMatrixShader) {
      this.boneMatrixShader.dispose();
      this.boneMatrixShader = null;
    }

    this.boneMatrixScene = null;
    this.boneMatrixCamera = null;

    // 清除调试网格
    for (const mesh of this.debugMeshes) {
      mesh.parent?.remove(mesh);
      mesh.geometry.dispose();
      (mesh.material as THREE.Material).dispose();
    }
    this.debugMeshes = [];

    // 清除调试材质
    if (this.debugMaterial) {
      this.debugMaterial.dispose();
      this.debugMaterial = null;
    }

    this.initialized = false;
  }
}
