/**
 * 物理场景导出器
 * 用于导出物理场景数据
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Scene } from '../../scene/Scene';
import { Entity } from '../../core/Entity';
import { PhysicsSystem } from '../PhysicsSystem';
import { PhysicsBody } from '../PhysicsBody';
import { PhysicsCollider } from '../PhysicsCollider';
import { PhysicsBodyComponent } from '../components/PhysicsBodyComponent';
import { PhysicsColliderComponent } from '../components/PhysicsColliderComponent';
import { PhysicsConstraintComponent } from '../components/PhysicsConstraintComponent';
import { PhysicsWorldComponent } from '../components/PhysicsWorldComponent';
import { Debug } from '../../utils/Debug';

/**
 * 物理场景导出选项
 */
export interface PhysicsSceneExportOptions {
  /** 是否包含物理体 */
  includeBodies?: boolean;
  /** 是否包含碰撞器 */
  includeColliders?: boolean;
  /** 是否包含约束 */
  includeConstraints?: boolean;
  /** 是否包含物理世界 */
  includeWorld?: boolean;
  /** 是否包含材质 */
  includeMaterials?: boolean;
  /** 是否美化JSON输出 */
  prettyPrint?: boolean;
  /** 是否包含元数据 */
  includeMetadata?: boolean;
  /** 自定义元数据 */
  metadata?: Record<string, any>;
}

/**
 * 物理场景导出数据
 */
export interface PhysicsSceneExportData {
  /** 版本 */
  version: string;
  /** 元数据 */
  metadata?: Record<string, any>;
  /** 物理世界数据 */
  world?: any;
  /** 物理体数据 */
  bodies?: any[];
  /** 碰撞器数据 */
  colliders?: any[];
  /** 约束数据 */
  constraints?: any[];
  /** 材质数据 */
  materials?: any[];
}

/**
 * 物理场景导出器
 */
export class PhysicsSceneExporter {
  /** 物理系统 */
  private physicsSystem: PhysicsSystem;

  /**
   * 创建物理场景导出器
   * @param physicsSystem 物理系统
   */
  constructor(physicsSystem: PhysicsSystem) {
    this.physicsSystem = physicsSystem;
  }

  /**
   * 导出场景
   * @param scene 场景
   * @param options 导出选项
   * @returns 导出数据
   */
  public export(scene: Scene, options: PhysicsSceneExportOptions = {}): PhysicsSceneExportData {
    // 默认选项
    const defaultOptions: PhysicsSceneExportOptions = {
      includeBodies: true,
      includeColliders: true,
      includeConstraints: true,
      includeWorld: true,
      includeMaterials: true,
      prettyPrint: false,
      includeMetadata: true
    };

    // 合并选项
    const exportOptions = { ...defaultOptions, ...options };

    // 创建导出数据
    const exportData: PhysicsSceneExportData = {
      version: '1.0'
    };

    // 添加元数据
    if (exportOptions.includeMetadata) {
      exportData.metadata = {
        type: 'PhysicsScene',
        generator: 'PhysicsSceneExporter',
        date: new Date().toISOString(),
        ...exportOptions.metadata
      };
    }

    // 导出物理世界
    if (exportOptions.includeWorld) {
      exportData.world = this.exportWorld(scene);
    }

    // 导出物理体
    if (exportOptions.includeBodies) {
      exportData.bodies = this.exportBodies(scene);
    }

    // 导出碰撞器
    if (exportOptions.includeColliders) {
      exportData.colliders = this.exportColliders(scene);
    }

    // 导出约束
    if (exportOptions.includeConstraints) {
      exportData.constraints = this.exportConstraints(scene);
    }

    // 导出材质
    if (exportOptions.includeMaterials) {
      exportData.materials = this.exportMaterials();
    }

    return exportData;
  }

  /**
   * 导出为JSON
   * @param scene 场景
   * @param options 导出选项
   * @returns JSON字符串
   */
  public exportToJSON(scene: Scene, options: PhysicsSceneExportOptions = {}): string {
    const exportData = this.export(scene, options);

    return JSON.stringify(exportData, (key, value) => {
      // 处理THREE.Vector3和THREE.Quaternion
      if (value instanceof THREE.Vector3) {
        return { x: value.x, y: value.y, z: value.z, __type: 'Vector3' };
      } else if (value instanceof THREE.Quaternion) {
        return { x: value.x, y: value.y, z: value.z, w: value.w, __type: 'Quaternion' };
      }
      return value;
    }, options.prettyPrint ? 2 : undefined);
  }

  /**
   * 导出物理世界
   * @param scene 场景
   * @returns 物理世界数据
   */
  private exportWorld(scene: Scene): any {
    // 查找场景中的物理世界组件
    const worldEntity = scene.getAllEntities().find(entity => entity.hasComponent(PhysicsWorldComponent.type));

    if (!worldEntity) {
      return null;
    }

    const worldComponent = worldEntity.getComponent<PhysicsWorldComponent>(PhysicsWorldComponent.type);
    if (!worldComponent) {
      return null;
    }

    // 导出物理世界数据
    return {
      entityId: worldEntity.id,
      gravity: worldComponent.getGravity(),
      allowSleep: worldComponent.getAllowSleep(),
      iterations: worldComponent.getIterations(),
      broadphase: worldComponent.getBroadphaseAlgorithm(),
      gridBroadphaseSize: worldComponent.getGridBroadphaseSize(),
      defaultFriction: worldComponent.getDefaultFriction(),
      defaultRestitution: worldComponent.getDefaultRestitution()
    };
  }

  /**
   * 导出物理体
   * @param scene 场景
   * @returns 物理体数据数组
   */
  private exportBodies(scene: Scene): any[] {
    const bodies: any[] = [];

    // 遍历场景中的所有实体
    for (const entity of scene.getAllEntities()) {
      const bodyComponent = entity.getComponent<PhysicsBodyComponent>(PhysicsBodyComponent.type);

      if (bodyComponent) {
        // 导出物理体数据
        bodies.push({
          entityId: entity.id,
          type: bodyComponent.getType(),
          mass: bodyComponent.getMass(),
          position: bodyComponent.getPosition(),
          quaternion: bodyComponent.getQuaternion(),
          linearVelocity: bodyComponent.getLinearVelocity(),
          angularVelocity: bodyComponent.getAngularVelocity(),
          linearDamping: bodyComponent.getLinearDamping(),
          angularDamping: bodyComponent.getAngularDamping(),
          allowSleep: bodyComponent.getAllowSleep(),
          sleepSpeedLimit: bodyComponent.getSleepSpeedLimit(),
          sleepTimeLimit: bodyComponent.getSleepTimeLimit(),
          fixedRotation: bodyComponent.getFixedRotation(),
          material: bodyComponent.getMaterial()?.name || 'default',
          collisionFilterGroup: bodyComponent.getCollisionFilterGroup(),
          collisionFilterMask: bodyComponent.getCollisionFilterMask(),
          enableCCD: bodyComponent.getEnableCCD()
        });
      }
    }

    return bodies;
  }

  /**
   * 导出碰撞器
   * @param scene 场景
   * @returns 碰撞器数据数组
   */
  private exportColliders(scene: Scene): any[] {
    const colliders: any[] = [];

    // 遍历场景中的所有实体
    for (const entity of scene.getAllEntities()) {
      const colliderComponent = entity.getComponent<PhysicsColliderComponent>(PhysicsColliderComponent.type);

      if (colliderComponent) {
        // 导出碰撞器数据
        colliders.push({
          entityId: entity.id,
          type: colliderComponent.getType(),
          params: colliderComponent.getParams(),
          position: colliderComponent.getPosition(),
          quaternion: colliderComponent.getQuaternion(),
          material: colliderComponent.getMaterial()?.name || 'default',
          isTrigger: colliderComponent.isTrigger()
        });
      }
    }

    return colliders;
  }

  /**
   * 导出约束
   * @param scene 场景
   * @returns 约束数据数组
   */
  private exportConstraints(scene: Scene): any[] {
    const constraints: any[] = [];

    // 遍历场景中的所有实体
    for (const entity of scene.getAllEntities()) {
      const constraintComponent = entity.getComponent<PhysicsConstraintComponent>(PhysicsConstraintComponent.type);

      if (constraintComponent) {
        // 导出约束数据
        constraints.push({
          entityId: entity.id,
          type: constraintComponent.getConstraintType(),
          targetEntity: constraintComponent.getTargetEntity()?.id,
          collideConnected: constraintComponent.getCollideConnected(),
          pivotA: constraintComponent.getPivotA(),
          pivotB: constraintComponent.getPivotB(),
          axisA: constraintComponent.getAxisA(),
          axisB: constraintComponent.getAxisB(),
          maxForce: constraintComponent.getMaxForce()
        });
      }
    }

    return constraints;
  }

  /**
   * 导出材质
   * @returns 材质数据数组
   */
  private exportMaterials(): any[] {
    const materials: any[] = [];

    // 获取物理世界
    const world = this.physicsSystem.getPhysicsWorld();

    // 遍历物理世界中的所有材质
    for (const material of world.materials) {
      // 查找使用此材质的接触材质
      const contactMaterials: any[] = [];

      for (const contactMaterial of world.contactmaterials) {
        if (contactMaterial.materials[0] === material || contactMaterial.materials[1] === material) {
          contactMaterials.push({
            materialA: contactMaterial.materials[0].name,
            materialB: contactMaterial.materials[1].name,
            friction: contactMaterial.friction,
            restitution: contactMaterial.restitution,
            contactEquationStiffness: contactMaterial.contactEquationStiffness,
            contactEquationRelaxation: contactMaterial.contactEquationRelaxation,
            frictionEquationStiffness: contactMaterial.frictionEquationStiffness,
            frictionEquationRelaxation: contactMaterial.frictionEquationRelaxation
          });
        }
      }

      // 导出材质数据
      materials.push({
        name: material.name,
        contactMaterials
      });
    }

    return materials;
  }
}
