/**
 * 圆锥扭转约束
 * 限制两个物体之间的相对旋转，类似于万向节
 */
import * as THREE from 'three';
import * as CANNON from 'cannon-es';
import { Entity } from '../../core/Entity';
import { PhysicsConstraint, ConstraintType } from './PhysicsConstraint';

/**
 * 圆锥扭转约束选项
 */
export interface ConeTwistConstraintOptions {
  /** 源物体上的连接点（局部坐标） */
  pivotA?: THREE.Vector3;
  /** 目标物体上的连接点（局部坐标） */
  pivotB?: THREE.Vector3;
  /** 源物体上的轴（局部坐标） */
  axisA?: THREE.Vector3;
  /** 目标物体上的轴（局部坐标） */
  axisB?: THREE.Vector3;
  /** 最大力 */
  maxForce?: number;
  /** 是否允许连接的物体之间碰撞 */
  collideConnected?: boolean;
  /** 圆锥角度（弧度） */
  angle?: number;
  /** 扭转角度（弧度） */
  twistAngle?: number;
}

/**
 * 圆锥扭转约束
 */
export class ConeTwistConstraint extends PhysicsConstraint {
  /** 组件类型 */
  public static readonly type: string = 'ConeTwistConstraint';

  /** 源物体上的连接点（局部坐标） */
  private pivotA: CANNON.Vec3;

  /** 目标物体上的连接点（局部坐标） */
  private pivotB: CANNON.Vec3;

  /** 源物体上的轴（局部坐标） */
  private axisA: CANNON.Vec3;

  /** 目标物体上的轴（局部坐标） */
  private axisB: CANNON.Vec3;

  /** 最大力 */
  private maxForce: number;

  /** 圆锥角度（弧度） */
  private angle: number;

  /** 扭转角度（弧度） */
  private twistAngle: number;

  /**
   * 创建圆锥扭转约束
   * @param targetEntity 目标实体
   * @param options 约束选项
   */
  constructor(targetEntity: Entity | null = null, options: ConeTwistConstraintOptions = {}) {
    super(ConstraintType.CONE_TWIST, targetEntity, options);

    // 设置源物体上的连接点
    const pivotA = options.pivotA || new THREE.Vector3(0, 0, 0);
    this.pivotA = new CANNON.Vec3(pivotA.x, pivotA.y, pivotA.z);

    // 设置目标物体上的连接点
    const pivotB = options.pivotB || new THREE.Vector3(0, 0, 0);
    this.pivotB = new CANNON.Vec3(pivotB.x, pivotB.y, pivotB.z);

    // 设置源物体上的轴
    const axisA = options.axisA || new THREE.Vector3(1, 0, 0);
    this.axisA = new CANNON.Vec3(axisA.x, axisA.y, axisA.z);
    this.axisA.normalize();

    // 设置目标物体上的轴
    const axisB = options.axisB || new THREE.Vector3(1, 0, 0);
    this.axisB = new CANNON.Vec3(axisB.x, axisB.y, axisB.z);
    this.axisB.normalize();

    // 设置最大力
    this.maxForce = options.maxForce !== undefined ? options.maxForce : 1e6;

    // 设置角度限制
    this.angle = options.angle !== undefined ? options.angle : Math.PI / 4;
    this.twistAngle = options.twistAngle !== undefined ? options.twistAngle : Math.PI / 4;
  }

  /**
   * 创建约束
   */
  protected createConstraint(): void {
    // 获取源物体和目标物体
    const bodyA = this.getSourceBody();
    const bodyB = this.getTargetBody();

    // 如果没有源物体或目标物体，则无法创建约束
    if (!bodyA || !bodyB) {
      console.warn('无法创建圆锥扭转约束：缺少源物体或目标物体');
      return;
    }

    // CANNON.js没有内置的ConeTwistConstraint，使用PointToPointConstraint作为替代
    // 或者使用HingeConstraint来模拟
    this.constraint = new CANNON.PointToPointConstraint(bodyA, this.pivotA, bodyB, this.pivotB, this.maxForce);
    this.constraint.collideConnected = this.collideConnected;
  }

  /**
   * 设置源物体上的连接点
   * @param pivot 连接点（局部坐标）
   */
  public setPivotA(pivot: THREE.Vector3): void {
    this.pivotA.set(pivot.x, pivot.y, pivot.z);

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取源物体上的连接点
   * @returns 连接点（局部坐标）
   */
  public getPivotA(): THREE.Vector3 {
    return new THREE.Vector3(this.pivotA.x, this.pivotA.y, this.pivotA.z);
  }

  /**
   * 设置目标物体上的连接点
   * @param pivot 连接点（局部坐标）
   */
  public setPivotB(pivot: THREE.Vector3): void {
    this.pivotB.set(pivot.x, pivot.y, pivot.z);

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取目标物体上的连接点
   * @returns 连接点（局部坐标）
   */
  public getPivotB(): THREE.Vector3 {
    return new THREE.Vector3(this.pivotB.x, this.pivotB.y, this.pivotB.z);
  }

  /**
   * 设置源物体上的轴
   * @param axis 轴（局部坐标）
   */
  public setAxisA(axis: THREE.Vector3): void {
    this.axisA.set(axis.x, axis.y, axis.z);
    this.axisA.normalize();

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取源物体上的轴
   * @returns 轴（局部坐标）
   */
  public getAxisA(): THREE.Vector3 {
    return new THREE.Vector3(this.axisA.x, this.axisA.y, this.axisA.z);
  }

  /**
   * 设置目标物体上的轴
   * @param axis 轴（局部坐标）
   */
  public setAxisB(axis: THREE.Vector3): void {
    this.axisB.set(axis.x, axis.y, axis.z);
    this.axisB.normalize();

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取目标物体上的轴
   * @returns 轴（局部坐标）
   */
  public getAxisB(): THREE.Vector3 {
    return new THREE.Vector3(this.axisB.x, this.axisB.y, this.axisB.z);
  }

  /**
   * 设置圆锥角度
   * @param angle 角度（弧度）
   */
  public setAngle(angle: number): void {
    this.angle = angle;
    // 注意：由于使用PointToPointConstraint替代，角度限制功能受限
  }

  /**
   * 获取圆锥角度
   * @returns 角度（弧度）
   */
  public getAngle(): number {
    return this.angle;
  }

  /**
   * 设置扭转角度
   * @param angle 角度（弧度）
   */
  public setTwistAngle(angle: number): void {
    this.twistAngle = angle;
    // 注意：由于使用PointToPointConstraint替代，角度限制功能受限
  }

  /**
   * 获取扭转角度
   * @returns 角度（弧度）
   */
  public getTwistAngle(): number {
    return this.twistAngle;
  }

  /**
   * 设置最大力
   * @param maxForce 最大力
   */
  public setMaxForce(maxForce: number): void {
    this.maxForce = maxForce;

    // 如果约束已创建，需要重新创建
    this.recreateConstraint();
  }

  /**
   * 获取最大力
   * @returns 最大力
   */
  public getMaxForce(): number {
    return this.maxForce;
  }

  /**
   * 重新创建约束
   */
  private recreateConstraint(): void {
    if (this.initialized && this.constraint && this.world) {
      this.world.removeConstraint(this.constraint);
      this.constraint = null;
      this.initialized = false;
      this.initialize(this.world);
    }
  }
}
