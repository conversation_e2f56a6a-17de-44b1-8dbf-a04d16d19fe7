/**
 * 输入组件
 * 用于处理实体的输入
 */
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { InputAction } from '../InputAction';
import { InputBinding } from '../InputBinding';
import { InputManager } from '../InputManager';
import { InputMapping } from '../InputMapping';

/**
 * 输入组件选项
 */
export interface InputComponentOptions {
  /** 是否启用 */
  enabled?: boolean;
  /** 输入动作列表 */
  actions?: InputAction[];
  /** 输入绑定列表 */
  bindings?: InputBinding[];
  /** 输入映射列表 */
  mappings?: InputMapping[];
}

/**
 * 输入组件
 */
export class InputComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'InputComponent';

  /** 是否启用 */
  private enabled: boolean = true;

  /** 输入动作映射 */
  private actions: Map<string, InputAction> = new Map();

  /** 输入绑定映射 */
  private bindings: Map<string, InputBinding> = new Map();

  /** 输入映射映射 */
  private mappings: Map<string, InputMapping> = new Map();

  /** 输入管理器 */
  private inputManager: InputManager;

  /**
   * 创建输入组件
   * @param options 选项
   */
  constructor(options: InputComponentOptions = {}) {
    super(InputComponent.TYPE);

    this.enabled = options.enabled !== undefined ? options.enabled : true;
    this.inputManager = InputManager.getInstance();

    // 添加输入动作
    if (options.actions) {
      for (const action of options.actions) {
        this.addAction(action);
      }
    }

    // 添加输入绑定
    if (options.bindings) {
      for (const binding of options.bindings) {
        this.addBinding(binding);
      }
    }

    // 添加输入映射
    if (options.mappings) {
      for (const mapping of options.mappings) {
        this.addMapping(mapping);
      }
    }
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled) return;

    // 处理输入动作
    this.processActions();
  }

  /**
   * 处理输入动作
   */
  private processActions(): void {
    // 处理所有动作
    for (const action of this.actions.values()) {
      // 获取动作的绑定
      const binding = this.bindings.get(action.getName());
      if (!binding) continue;

      // 获取绑定的映射
      const mapping = this.mappings.get(binding.getMappingName());
      if (!mapping) continue;

      // 获取映射的设备
      const device = this.inputManager.getDevice(mapping.getDeviceName());
      if (!device) continue;

      // 检查设备输入是否满足映射条件
      const value = mapping.evaluate(device);

      // 更新动作状态
      action.update(value);
    }
  }

  /**
   * 添加输入动作
   * @param action 输入动作
   */
  public addAction(action: InputAction): void {
    this.actions.set(action.getName(), action);
  }

  /**
   * 获取输入动作
   * @param name 动作名称
   * @returns 输入动作
   */
  public getAction<T extends InputAction>(name: string): T | undefined {
    return this.actions.get(name) as T | undefined;
  }

  /**
   * 添加输入绑定
   * @param binding 输入绑定
   */
  public addBinding(binding: InputBinding): void {
    this.bindings.set(binding.getName(), binding);
  }

  /**
   * 获取输入绑定
   * @param name 绑定名称
   * @returns 输入绑定
   */
  public getBinding(name: string): InputBinding | undefined {
    return this.bindings.get(name);
  }

  /**
   * 添加输入映射
   * @param mapping 输入映射
   */
  public addMapping(mapping: InputMapping): void {
    this.mappings.set(mapping.getName(), mapping);
  }

  /**
   * 获取输入映射
   * @param name 映射名称
   * @returns 输入映射
   */
  public getMapping(name: string): InputMapping | undefined {
    return this.mappings.get(name);
  }

  /**
   * 设置是否启用
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 检查是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 获取所有输入动作
   * @returns 输入动作列表
   */
  public getActions(): InputAction[] {
    return Array.from(this.actions.values());
  }

  /**
   * 获取所有输入绑定
   * @returns 输入绑定列表
   */
  public getBindings(): InputBinding[] {
    return Array.from(this.bindings.values());
  }

  /**
   * 获取所有输入映射
   * @returns 输入映射列表
   */
  public getMappings(): InputMapping[] {
    return Array.from(this.mappings.values());
  }

  /**
   * 清除所有输入动作
   */
  public clearActions(): void {
    this.actions.clear();
  }

  /**
   * 清除所有输入绑定
   */
  public clearBindings(): void {
    this.bindings.clear();
  }

  /**
   * 清除所有输入映射
   */
  public clearMappings(): void {
    this.mappings.clear();
  }

  /**
   * 清除所有输入
   */
  public clearAll(): void {
    this.clearActions();
    this.clearBindings();
    this.clearMappings();
  }
}
