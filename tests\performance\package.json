{"name": "ir-engine-performance-tests", "version": "1.0.0", "description": "DL（Digital Learning）引擎性能测试", "main": "index.js", "scripts": {"test": "k6 run", "test:api-gateway": "k6 run src/api-gateway.js", "test:user-service": "k6 run src/user-service.js", "test:project-service": "k6 run src/project-service.js", "test:asset-service": "k6 run src/asset-service.js", "test:end-to-end": "k6 run src/end-to-end.js", "test:cache": "k6 run src/cache-performance.js", "test:compression": "k6 run src/compression-performance.js", "test:batch": "k6 run src/batch-performance.js", "test:service-cache": "k6 run src/service-cache-performance.js", "test:load-balancer": "k6 run src/load-balancer-performance.js", "report": "node src/generate-report.js"}, "keywords": ["performance", "testing", "load-testing"], "author": "IR Engine Team", "license": "CPAL", "dependencies": {"k6": "^0.0.0", "dotenv": "^16.3.1", "chart.js": "^4.4.0", "handlebars": "^4.7.8"}}