/**
 * UIComponent.ts
 *
 * UI组件类，用于管理UI元素的基本属性和行为
 */

import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { Vector2, Vector3 } from 'three';
import { IUIElement, UILayoutType } from '../interfaces/IUIElement';

/**
 * UI组件类型
 */
export enum UIComponentType {
  BASE = 'base',
  CONTAINER = 'container',
  BUTTON = 'button',
  TEXT = 'text',
  IMAGE = 'image',
  INPUT = 'input',
  SLIDER = 'slider',
  CHECKBOX = 'checkbox',
  DROPDOWN = 'dropdown',
  PANEL = 'panel',
  WINDOW = 'window',
  CUSTOM = 'custom'
}

/**
 * UI组件属性
 */
export interface UIComponentProps {
  // 基本属性
  id?: string;
  type?: UIComponentType;
  visible?: boolean;
  interactive?: boolean;
  position?: Vector3 | Vector2;
  size?: Vector2;
  opacity?: number;
  zIndex?: number;

  // 布局属性
  layoutType?: UILayoutType;
  layoutParams?: any;

  // 样式属性
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  padding?: number | { top?: number, right?: number, bottom?: number, left?: number };
  margin?: number | { top?: number, right?: number, bottom?: number, left?: number };

  // 事件处理
  onClick?: (event: any) => void;
  onHover?: (event: any) => void;
  onDragStart?: (event: any) => void;
  onDrag?: (event: any) => void;
  onDragEnd?: (event: any) => void;

  // 其他属性
  data?: any;
  tags?: string[];
  is3D?: boolean;
}

/**
 * UI组件类
 * 用于管理UI元素的基本属性和行为
 */
export class UIComponent extends Component implements IUIElement {
  // IUIElement接口实现
  id: string;
  entity: Entity;
  parent?: IUIElement;
  children: IUIElement[] = [];
  visible: boolean = true;
  interactive: boolean = true;
  position: Vector3 | Vector2;
  size: Vector2;
  opacity: number = 1.0;

  // 额外属性
  uiType: UIComponentType = UIComponentType.BASE;
  zIndex: number = 0;
  layoutType: UILayoutType = UILayoutType.NONE;
  layoutParams: any = {};
  backgroundColor?: string;
  borderColor?: string;
  borderWidth: number = 0;
  borderRadius: number = 0;
  padding: { top: number, right: number, bottom: number, left: number } = { top: 0, right: 0, bottom: 0, left: 0 };
  margin: { top: number, right: number, bottom: number, left: number } = { top: 0, right: 0, bottom: 0, left: 0 };
  data: any = {};
  tags: string[] = [];
  is3D: boolean = false;

  // 事件处理器
  private eventHandlers: Map<string, (event: any) => void> = new Map();

  /**
   * 构造函数
   * @param props UI组件属性
   */
  constructor(props: UIComponentProps = {}) {
    // 调用基类构造函数，传入组件类型名称
    super('UIComponent');

    // 设置实体引用（将在组件添加到实体时设置）
    this.entity = null as any; // 临时设置，将在onAttach中正确设置
    this.id = props.id || `ui-${Date.now()}`;
    this.uiType = props.type || UIComponentType.BASE;
    this.visible = props.visible !== undefined ? props.visible : true;
    this.interactive = props.interactive !== undefined ? props.interactive : true;
    this.position = props.position || new Vector3(0, 0, 0);
    this.size = props.size || new Vector2(100, 100);
    this.opacity = props.opacity !== undefined ? props.opacity : 1.0;
    this.zIndex = props.zIndex || 0;
    this.layoutType = props.layoutType || UILayoutType.NONE;
    this.layoutParams = props.layoutParams || {};
    this.backgroundColor = props.backgroundColor;
    this.borderColor = props.borderColor;
    this.borderWidth = props.borderWidth || 0;
    this.borderRadius = props.borderRadius || 0;
    this.is3D = props.is3D || false;

    // 设置内边距
    if (props.padding !== undefined) {
      if (typeof props.padding === 'number') {
        this.padding = { top: props.padding, right: props.padding, bottom: props.padding, left: props.padding };
      } else {
        this.padding = {
          top: props.padding.top || 0,
          right: props.padding.right || 0,
          bottom: props.padding.bottom || 0,
          left: props.padding.left || 0
        };
      }
    }

    // 设置外边距
    if (props.margin !== undefined) {
      if (typeof props.margin === 'number') {
        this.margin = { top: props.margin, right: props.margin, bottom: props.margin, left: props.margin };
      } else {
        this.margin = {
          top: props.margin.top || 0,
          right: props.margin.right || 0,
          bottom: props.margin.bottom || 0,
          left: props.margin.left || 0
        };
      }
    }

    // 设置标签
    this.tags = props.tags || [];

    // 设置数据
    this.data = props.data || {};

    // 设置事件处理器
    if (props.onClick) this.addEventListener('click', props.onClick);
    if (props.onHover) this.addEventListener('hover', props.onHover);
    if (props.onDragStart) this.addEventListener('dragstart', props.onDragStart);
    if (props.onDrag) this.addEventListener('drag', props.onDrag);
    if (props.onDragEnd) this.addEventListener('dragend', props.onDragEnd);
  }

  /**
   * 当组件附加到实体时调用
   */
  protected onAttach(): void {
    const entity = this.getEntity();
    if (entity) {
      this.entity = entity;
      // 如果ID还是默认的，则使用实体ID
      if (this.id.startsWith('ui-') && this.id.includes(Date.now().toString())) {
        this.id = `ui-${entity.id}`;
      }
    }
  }

  /**
   * 添加子元素
   * @param child 要添加的子元素
   */
  addChild(child: IUIElement): void {
    if (!this.children.includes(child)) {
      this.children.push(child);
      if (child instanceof UIComponent) {
        child.parent = this;
      }
    }
  }

  /**
   * 移除子元素
   * @param child 要移除的子元素
   */
  removeChild(child: IUIElement): void {
    const index = this.children.indexOf(child);
    if (index !== -1) {
      this.children.splice(index, 1);
      if (child instanceof UIComponent) {
        child.parent = undefined;
      }
    }
  }

  /**
   * 更新UI元素
   * @param deltaTime 时间增量
   */
  update(deltaTime: number): void {
    // 更新子元素
    for (const child of this.children) {
      child.update(deltaTime);
    }
  }

  /**
   * 渲染UI元素
   */
  render(): void {
    // 渲染子元素
    for (const child of this.children) {
      child.render();
    }
  }

  /**
   * 销毁UI元素
   */
  dispose(): void {
    // 销毁子元素
    for (const child of [...this.children]) {
      child.dispose();
    }

    // 清空子元素列表
    this.children = [];

    // 清空事件处理器
    this.eventHandlers.clear();
  }

  /**
   * 添加事件监听器
   * @param eventType 事件类型
   * @param handler 事件处理函数
   */
  addEventListener(eventType: string, handler: (event: any) => void): void {
    this.eventHandlers.set(eventType, handler);
  }

  /**
   * 移除事件监听器
   * @param eventType 事件类型
   */
  removeEventListener(eventType: string): void {
    this.eventHandlers.delete(eventType);
  }

  /**
   * 触发事件
   * @param eventType 事件类型
   * @param eventData 事件数据
   */
  triggerEvent(eventType: string, eventData: any): void {
    const handler = this.eventHandlers.get(eventType);
    if (handler) {
      handler(eventData);
    }
  }

  /**
   * 设置位置
   * @param position 新位置
   */
  setPosition(position: Vector3 | Vector2): void {
    this.position = position;
  }

  /**
   * 设置尺寸
   * @param size 新尺寸
   */
  setSize(size: Vector2): void {
    this.size = size;
  }

  /**
   * 设置可见性
   * @param visible 是否可见
   */
  setVisible(visible: boolean): void {
    this.visible = visible;
  }

  /**
   * 设置交互性
   * @param interactive 是否可交互
   */
  setInteractive(interactive: boolean): void {
    this.interactive = interactive;
  }

  /**
   * 设置透明度
   * @param opacity 透明度值
   */
  setOpacity(opacity: number): void {
    this.opacity = Math.max(0, Math.min(1, opacity));
  }

  /**
   * 添加标签
   * @param tag 要添加的标签
   */
  addTag(tag: string): void {
    if (!this.tags.includes(tag)) {
      this.tags.push(tag);
    }
  }

  /**
   * 移除标签
   * @param tag 要移除的标签
   */
  removeTag(tag: string): void {
    const index = this.tags.indexOf(tag);
    if (index !== -1) {
      this.tags.splice(index, 1);
    }
  }

  /**
   * 检查是否有指定标签
   * @param tag 要检查的标签
   */
  hasTag(tag: string): boolean {
    return this.tags.includes(tag);
  }
}
