{"name": "ir-engine-integration-tests", "version": "1.0.0", "description": "DL（Digital Learning）引擎微服务集成测试", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:user-project": "jest --testPathPattern=user-project", "test:asset-render": "jest --testPathPattern=asset-render", "test:transactions": "jest --testPathPattern=transactions", "test:all": "jest --runInBand", "docker:up": "docker-compose -f docker-compose.test.yml up -d", "docker:down": "docker-compose -f docker-compose.test.yml down", "pretest": "npm run docker:up", "posttest": "npm run docker:down"}, "keywords": ["testing", "integration", "microservices"], "author": "IR Engine Team", "license": "CPAL", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "wait-for-expect": "^3.0.2"}, "devDependencies": {"@types/jest": "^29.5.6", "@types/node": "^20.8.9"}}